import 'package:flutter/material.dart';
import '../order/purchase_page/purchase_order_create.dart';
import '../me/addr_lib/address_book_page.dart';

/// 地址功能演示页面
/// 
/// 这个页面展示了地址库功能的完整使用流程：
/// 1. 显示当前选中的地址
/// 2. 提供按钮跳转到订单创建页面
/// 3. 提供按钮直接打开地址库
class AddressDemoPage extends StatefulWidget {
  const AddressDemoPage({Key? key}) : super(key: key);

  @override
  State<AddressDemoPage> createState() => _AddressDemoPageState();
}

class _AddressDemoPageState extends State<AddressDemoPage> {
  AddressItem? _selectedAddress;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('地址功能演示'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 1,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 功能说明
            _buildFeatureDescription(),
            const SizedBox(height: 24),
            
            // 当前选中地址显示
            _buildCurrentAddressSection(),
            const SizedBox(height: 24),
            
            // 功能按钮
            _buildActionButtons(),
            const SizedBox(height: 24),
            
            // 使用说明
            _buildUsageInstructions(),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureDescription() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  '功能介绍',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              '地址库功能支持：\n'
              '• 地址选择和回显\n'
              '• 默认地址自动显示\n'
              '• 页面间数据传递\n'
              '• 双模式UI支持（选择/管理）',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentAddressSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.location_on, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  '当前选中地址',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Text(
                _selectedAddress != null
                    ? '${_selectedAddress!.name}\n${_selectedAddress!.phone}'
                    : '暂未选择地址\n点击下方按钮选择地址',
                style: TextStyle(
                  fontSize: 14,
                  color: _selectedAddress != null ? Colors.black87 : Colors.grey[600],
                  height: 1.4,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // 订单创建页面按钮
        ElevatedButton.icon(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const PurchaseOrderPage(),
              ),
            );
          },
          icon: const Icon(Icons.shopping_cart),
          label: const Text('进入订单创建页面'),
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF7B68EE),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
        const SizedBox(height: 12),
        
        // 地址库管理按钮
        OutlinedButton.icon(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const AddressBookPage(isSelectionMode: false),
              ),
            );
          },
          icon: const Icon(Icons.edit_location),
          label: const Text('地址库管理'),
          style: OutlinedButton.styleFrom(
            foregroundColor: const Color(0xFF7B68EE),
            padding: const EdgeInsets.symmetric(vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
        const SizedBox(height: 12),
        
        // 地址选择按钮
        OutlinedButton.icon(
          onPressed: _selectAddress,
          icon: const Icon(Icons.location_searching),
          label: const Text('直接选择地址'),
          style: OutlinedButton.styleFrom(
            foregroundColor: Colors.green,
            padding: const EdgeInsets.symmetric(vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildUsageInstructions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.help_outline, color: Colors.orange),
                const SizedBox(width: 8),
                Text(
                  '使用说明',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.orange,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildInstructionItem('1', '点击"进入订单创建页面"体验完整流程'),
            _buildInstructionItem('2', '在订单页面点击"地址库"按钮选择地址'),
            _buildInstructionItem('3', '选择地址后会自动返回并显示选中地址'),
            _buildInstructionItem('4', '也可以直接点击"地址库管理"管理地址'),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructionItem(String number, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: Colors.orange,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                number,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 直接选择地址
  void _selectAddress() async {
    final selectedAddress = await Navigator.push<AddressItem>(
      context,
      MaterialPageRoute(
        builder: (context) => const AddressBookPage(isSelectionMode: true),
      ),
    );

    if (selectedAddress != null) {
      setState(() {
        _selectedAddress = selectedAddress;
      });
      
      // 显示选择成功提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('已选择地址：${selectedAddress.name}'),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }
}
