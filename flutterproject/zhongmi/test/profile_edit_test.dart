import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:zhongmi/me/profile_page.dart';
import 'package:zhongmi/me/user_info/user_profile_edit_page.dart';

void main() {
  group('用户资料编辑功能测试', () {
    testWidgets('点击用户信息卡片应该跳转到编辑页面', (WidgetTester tester) async {
      // 构建ProfilePage
      await tester.pumpWidget(
        MaterialApp(
          home: const ProfilePage(),
        ),
      );

      // 查找用户信息卡片
      final userInfoCard = find.byType(GestureDetector).first;
      expect(userInfoCard, findsOneWidget);

      // 点击用户信息卡片
      await tester.tap(userInfoCard);
      await tester.pumpAndSettle();

      // 验证是否跳转到编辑页面
      expect(find.byType(UserProfileEditPage), findsOneWidget);
      expect(find.text('编辑资料'), findsOneWidget);
    });

    testWidgets('编辑页面应该显示正确的用户信息', (WidgetTester tester) async {
      // 构建UserProfileEditPage
      await tester.pumpWidget(
        const MaterialApp(
          home: UserProfileEditPage(),
        ),
      );

      // 验证页面标题
      expect(find.text('编辑资料'), findsOneWidget);
      
      // 验证保存按钮
      expect(find.text('保存'), findsOneWidget);
      
      // 验证输入框标签
      expect(find.text('用户名'), findsOneWidget);
      expect(find.text('手机号'), findsOneWidget);
      expect(find.text('邮箱'), findsOneWidget);
      
      // 验证头像更换提示
      expect(find.text('点击更换头像'), findsOneWidget);
    });

    testWidgets('头像容器应该显示编辑图标', (WidgetTester tester) async {
      // 构建ProfilePage
      await tester.pumpWidget(
        const MaterialApp(
          home: ProfilePage(),
        ),
      );

      // 查找编辑图标
      final editIcon = find.byIcon(Icons.edit);
      expect(editIcon, findsOneWidget);

      // 验证编辑图标在Stack中（表示它是头像上的浮动图标）
      final stackWidget = find.ancestor(
        of: editIcon,
        matching: find.byType(Stack),
      );
      expect(stackWidget, findsOneWidget);
    });

    testWidgets('点击保存按钮应该显示成功消息', (WidgetTester tester) async {
      // 构建UserProfileEditPage
      await tester.pumpWidget(
        const MaterialApp(
          home: UserProfileEditPage(),
        ),
      );

      // 点击保存按钮
      final saveButton = find.text('保存');
      await tester.tap(saveButton);
      await tester.pump(); // 触发状态更新
      await tester.pump(const Duration(milliseconds: 100)); // 等待SnackBar显示

      // 验证是否显示成功消息
      expect(find.text('保存成功'), findsOneWidget);
    });
  });
}
